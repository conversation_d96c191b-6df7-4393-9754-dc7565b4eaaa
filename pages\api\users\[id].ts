/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import axios from 'axios';
import { authOptions } from '../auth/[...nextauth]';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data';
import { handleBackendError } from '../../../src/utils/handle-backend-error';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    res.status(405).json({
      success: false,
      error: {
        type: 'METHOD_NOT_ALLOWED',
        message: 'Method not allowed',
        code: 405,
      },
    });
    return;
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.accessToken) {
      res.status(401).json({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'Authentication required',
          code: 401,
        },
      });
      return;
    }

    const { id } = req.query;
    
    if (!id || Array.isArray(id)) {
      res.status(400).json({
        success: false,
        error: {
          type: 'VALIDATION_ERROR',
          message: 'User ID is required',
          code: 400,
        },
      });
      return;
    }

    const token = `Bearer ${session.accessToken}`;

    // Make request to backend with specific user ID
    const backendRes = await BACKEND_API.get(API_ENDPOINT.users.list, {
      params: { 
        id,
        detail: 'true', // Indicate this is a detail request
        _t: Date.now(), // Cache buster
      },
      headers: { 
        Authorization: token,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });

    const apiResponse = backendRes.data;

    // Validate response structure
    if (!apiResponse.data || !Array.isArray(apiResponse.data) || apiResponse.data.length === 0) {
      res.status(404).json({
        success: false,
        error: {
          type: 'NOT_FOUND',
          message: `User with ID ${id} not found`,
          code: 404,
        },
      });
      return;
    }

    // Find the specific user by ID to ensure we get the right one
    const user = apiResponse.data.find((u: any) => u.id === id);
    
    if (!user) {
      res.status(404).json({
        success: false,
        error: {
          type: 'NOT_FOUND',
          message: `User with ID ${id} not found`,
          code: 404,
        },
      });
      return;
    }

    // Return the user detail in the expected format
    res.status(200).json({
      success: true,
      data: {
        user,
      },
    });

  } catch (error: any) {
    // Handle token expiry from backend
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      res.status(401).json({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'Authentication token is invalid or expired',
          code: 401,
        },
      });
      return;
    }

    // Handle user not found
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      res.status(404).json({
        success: false,
        error: {
          type: 'NOT_FOUND',
          message: 'User not found',
          code: 404,
        },
      });
      return;
    }

    // Other backend errors
    const errorKey = handleBackendError(error, []);
    const statusCode = axios.isAxiosError(error) && error.response?.status
      ? error.response.status
      : 500;

    res.status(statusCode).json({
      success: false,
      error: {
        type: 'BACKEND_ERROR',
        message: error.response?.data?.message || 'Internal server error',
        code: statusCode,
        key: errorKey,
      },
    });
  }
}
